@startuml sprint5_presentation
!theme plain
skinparam backgroundColor white
skinparam actor {
    BackgroundColor lightblue
    BorderColor black
}
skinparam usecase {
    BackgroundColor lightgreen
    BorderColor black
}
skinparam system {
    BackgroundColor lightyellow
    BorderColor black
}

title Sprint 5: Notifications & System Polish

actor "Patient" as P
actor "Pharmacist" as Ph
actor "Mobile App" as M
actor "Web App" as W

rectangle "Sprint 5 Features" {
    package "Notification Management" {
        usecase "Receive Prescription Alerts" as UC1
        usecase "Receive Order Updates" as UC2
        usecase "Receive Delivery Notifications" as UC3
        usecase "Manage Notification Preferences" as UC4
        usecase "View Notification History" as UC5
    }
    
    package "Firebase Integration" {
        usecase "Configure FCM" as UC6
        usecase "Send Push Notifications" as UC7
        usecase "Handle Notification Tokens" as UC8
    }
    
    package "System Polish & QA" {
        usecase "Performance Optimization" as UC9
        usecase "Bug Fixes & Testing" as UC10
        usecase "UI/UX Improvements" as UC11
        usecase "Final Integration Testing" as UC12
    }
}

rectangle "Firebase Cloud Messaging" as FCM {
    usecase "Push Notification Service" as PUSH1
    usecase "Token Management" as PUSH2
}

' Patient connections
P --> UC1
P --> UC2
P --> UC3
P --> UC4
P --> UC5

' Pharmacist connections
Ph --> UC1
Ph --> UC7

' App connections
M --> UC6
M --> UC8
W --> UC6
W --> UC8

' System connections
UC6 --> FCM
UC7 --> PUSH1
UC8 --> PUSH2

note right of FCM
  Firebase integration for:
  - iOS Mobile App
  - Web Application
  - Backend Services
end note

@enduml
